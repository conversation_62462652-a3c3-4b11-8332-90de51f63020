"use client";

import React from "react";
import { Box } from "@mui/material";
import MessagingDashboard from "@/components/messaging/MessagingDashboard";
import { MessageProvider } from "@/contexts/MessageContext";

const MessagesPage: React.FC = () => {
	return (
		<MessageProvider>
			<Box
				sx={{
					height: "calc(100vh - 90px)", // Account for dashboard header
					maxHeight: "calc(100vh - 90px)",
					overflow: "hidden",
					display: "flex",
					flexDirection: "column",
				}}
			>
				<MessagingDashboard />
			</Box>
		</MessageProvider>
	);
};

export default MessagesPage;
